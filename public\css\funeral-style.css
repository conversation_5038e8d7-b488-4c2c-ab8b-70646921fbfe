/* Funeral Service Website Custom Styles */

/* Additional styling for funeral service theme */
.hero-section {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    position: relative;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

/* Card styling for respectful appearance */
.service-card, .package-card {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.service-card:hover, .package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-color: #2c3e50;
}

/* Respectful color scheme */
.text-primary {
    color: #2c3e50 !important;
}

.btn-primary {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border: none;
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(44, 62, 80, 0.3);
}

.btn-outline-primary {
    border: 2px solid #2c3e50;
    color: #2c3e50;
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
}

.btn-outline-primary:hover {
    background: #2c3e50;
    border-color: #2c3e50;
    transform: translateY(-2px);
}

/* Navigation styling */
.navbar-brand {
    font-weight: 600;
    color: #2c3e50 !important;
    font-size: 1.5rem;
}

.nav-link {
    color: #2c3e50 !important;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover, .nav-link.active {
    color: #34495e !important;
    transform: translateY(-1px);
}

/* Footer styling */
.footer {
    background: linear-gradient(135deg, #1a252f 0%, #2c3e50 100%);
    color: white;
}

.footer h5 {
    color: #ecf0f1;
    margin-bottom: 1rem;
}

.footer a {
    color: #bdc3c7;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: #ecf0f1;
}

/* Contact section styling */
.contact-info .fas {
    width: 20px;
    text-align: center;
}

/* Business hours styling */
.business-hours {
    font-size: 0.95rem;
}

.business-hours .alert-info {
    background-color: #e8f4f8;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Gallery styling */
.gallery-item {
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.gallery-item:hover .gallery-image {
    transform: scale(1.1);
}

.gallery-caption {
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-caption {
    transform: translateY(0);
}

/* Activity card styling */
.activity-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.activity-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.activity-overlay {
    transition: opacity 0.3s ease;
}

.activity-card:hover .activity-overlay {
    opacity: 1 !important;
}

.activity-card:hover .activity-cover {
    transform: scale(1.05);
}

/* Home page activity cards */
.activity-card-home {
    transition: all 0.3s ease;
}

.activity-card-home:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.home-activity-overlay {
    transition: opacity 0.3s ease;
}

.activity-card-home:hover .home-activity-overlay {
    opacity: 1 !important;
}

.activity-card-home:hover img {
    transform: scale(1.05);
}

/* Breadcrumb styling */
.breadcrumb-item + .breadcrumb-item::before {
    color: rgba(255, 255, 255, 0.5);
}

.breadcrumb-item a {
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

/* Gallery thumbnails styling */
.gallery-thumb {
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.gallery-thumb:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border-color: #2c3e50;
}

.active-thumbnail {
    border-color: #2c3e50 !important;
    box-shadow: 0 4px 12px rgba(44, 62, 80, 0.3) !important;
}

.gallery-thumb-caption {
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-thumb:hover .gallery-thumb-caption {
    transform: translateY(0);
}

.gallery-thumbnail:hover {
    transform: scale(1.05);
}

/* Modal thumbnails */
.modal-thumb {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.modal-thumb:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Main image styling */
.main-image {
    transition: all 0.3s ease;
}

.main-image:hover {
    transform: scale(1.02);
}

/* Activity detail page styling */
.activity-meta {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
}

/* Share buttons */
.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-secondary:hover {
    transform: translateY(-2px);
}

/* Related activities styling */
.related-activity-item {
    transition: all 0.3s ease;
    border-radius: 8px;
    padding: 0.75rem;
}

.related-activity-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

/* Form styling */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #2c3e50;
    box-shadow: 0 0 0 0.2rem rgba(44, 62, 80, 0.25);
}

/* Alert styling */
.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
    border-radius: 10px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-section {
        padding: 60px 0;
    }
    
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .btn-lg {
        padding: 12px 20px;
        font-size: 1rem;
    }
    
    .d-flex.gap-3 {
        flex-direction: column;
        gap: 1rem !important;
    }
    
    .d-flex.gap-3 .btn {
        width: 100%;
    }
}

/* Additional respectful touches */
.card-title {
    color: #2c3e50;
    font-weight: 600;
}

.text-muted {
    color: #6c757d !important;
}

/* Icon styling for funeral theme */
.fas.fa-heart, .fas.fa-praying-hands, .fas.fa-dove {
    color: #2c3e50;
}

/* Package card featured styling */
.package-card.border-warning {
    border-color: #f39c12 !important;
}

.badge.bg-warning {
    background-color: #f39c12 !important;
}

/* Modal styling */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
    border-radius: 15px 15px 0 0;
}

/* Table styling */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table-primary {
    background-color: #2c3e50;
    color: white;
}

.table-primary th {
    border-color: #34495e;
}

/* Smooth animations */
* {
    transition: all 0.3s ease;
}

/* Loading animation for images */
img {
    transition: opacity 0.3s ease;
}

img:not([src]) {
    opacity: 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #2c3e50;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #34495e;
}

/* Admin edit page styling */
.card-header h6 {
    color: #2c3e50;
    font-weight: 600;
}

.card-header .fas {
    color: #3498db;
}

.card-header .fa-star {
    color: #f39c12;
}

.card-header .fa-plus {
    color: #27ae60;
}

/* Preview cards styling */
#coverImagePreview .card {
    border: 2px solid #f39c12;
    box-shadow: 0 4px 8px rgba(243, 156, 18, 0.2);
}

#newImagesPreview .card {
    border: 2px solid #27ae60;
    box-shadow: 0 4px 8px rgba(39, 174, 96, 0.2);
}

/* Badge styling */
.badge.bg-success {
    background-color: #27ae60 !important;
}

/* Alert info styling */
.alert-info {
    background-color: #e8f4f8;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-info ul {
    padding-left: 1.2rem;
}

.alert-info li {
    margin-bottom: 0.3rem;
}
