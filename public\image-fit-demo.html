<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Fit Demo - SoloShop</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="css/funeral-style.css" rel="stylesheet">
    <style>
        .demo-section {
            margin-bottom: 3rem;
        }
        .demo-title {
            color: #2c3e50;
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="text-center mb-5">
            <h1 class="display-4 text-primary">Image Fit Demo</h1>
            <p class="lead">ตัวอย่างการใช้งาน CSS Classes สำหรับการปรับขนาดรูปภาพ</p>
        </div>

        <!-- Object Fit Cover Demo -->
        <div class="demo-section">
            <h2 class="demo-title">1. Object Fit: Cover (เต็มกรอบ - อาจตัดรูป)</h2>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-image-container img-size-medium">
                            <img src="https://picsum.photos/800/600" class="img-fit-cover" alt="Cover Example">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">รูปแนวนอน</h5>
                            <p class="card-text">รูปภาพจะเต็มกรอบแต่อาจถูกตัดส่วนบน-ล่าง</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-image-container img-size-medium">
                            <img src="https://picsum.photos/600/800" class="img-fit-cover" alt="Cover Example">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">รูปแนวตั้ง</h5>
                            <p class="card-text">รูปภาพจะเต็มกรอบแต่อาจถูกตัดส่วนซ้าย-ขวา</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-image-container img-size-medium">
                            <img src="https://picsum.photos/400/400" class="img-fit-cover" alt="Cover Example">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">รูปสี่เหลี่ยมจัตุรัส</h5>
                            <p class="card-text">รูปภาพจะแสดงผลได้ดีที่สุด</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="code-block">
                <strong>CSS Class:</strong> <code>img-fit-cover</code><br>
                <strong>HTML:</strong> <code>&lt;img class="img-fit-cover" src="..."&gt;</code>
            </div>
        </div>

        <!-- Object Fit Contain Demo -->
        <div class="demo-section">
            <h2 class="demo-title">2. Object Fit: Contain (แสดงทั้งหมด - อาจมีพื้นที่ว่าง)</h2>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-image-container img-size-medium">
                            <img src="https://picsum.photos/800/600" class="img-fit-contain" alt="Contain Example">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">รูปแนวนอน</h5>
                            <p class="card-text">รูปภาพแสดงทั้งหมด มีพื้นที่ว่างบน-ล่าง</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-image-container img-size-medium">
                            <img src="https://picsum.photos/600/800" class="img-fit-contain" alt="Contain Example">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">รูปแนวตั้ง</h5>
                            <p class="card-text">รูปภาพแสดงทั้งหมด มีพื้นที่ว่างซ้าย-ขวา</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-image-container img-size-medium">
                            <img src="https://picsum.photos/400/400" class="img-fit-contain" alt="Contain Example">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">รูปสี่เหลี่ยมจัตุรัส</h5>
                            <p class="card-text">รูปภาพแสดงผลพอดีกรอบ</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="code-block">
                <strong>CSS Class:</strong> <code>img-fit-contain</code><br>
                <strong>HTML:</strong> <code>&lt;img class="img-fit-contain" src="..."&gt;</code>
            </div>
        </div>

        <!-- Object Fit Fill Demo -->
        <div class="demo-section">
            <h2 class="demo-title">3. Object Fit: Fill (ยืดเต็มกรอบ - อาจบิดเบี้ยว)</h2>
            <div class="row g-4">
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-image-container img-size-medium">
                            <img src="https://picsum.photos/800/600" class="img-fit-fill" alt="Fill Example">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">รูปแนวนอน</h5>
                            <p class="card-text">รูปภาพถูกยืดให้สูงขึ้น อาจดูบิดเบี้ยว</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-image-container img-size-medium">
                            <img src="https://picsum.photos/600/800" class="img-fit-fill" alt="Fill Example">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">รูปแนวตั้ง</h5>
                            <p class="card-text">รูปภาพถูกยืดให้กว้างขึ้น อาจดูบิดเบี้ยว</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-image-container img-size-medium">
                            <img src="https://picsum.photos/400/400" class="img-fit-fill" alt="Fill Example">
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">รูปสี่เหลี่ยมจัตุรัส</h5>
                            <p class="card-text">รูปภาพแสดงผลปกติ</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="code-block">
                <strong>CSS Class:</strong> <code>img-fit-fill</code><br>
                <strong>HTML:</strong> <code>&lt;img class="img-fit-fill" src="..."&gt;</code>
            </div>
        </div>

        <!-- Interactive Demo -->
        <div class="demo-section">
            <h2 class="demo-title">4. Interactive Demo - ทดลองเปลี่ยนโหมดการแสดงผล</h2>
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">ทดลองเปลี่ยนโหมด</h5>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-primary active" onclick="changeDemoFit('contain')" id="demo-btn-contain">
                                        <i class="fas fa-expand-arrows-alt"></i> แสดงทั้งหมด
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" onclick="changeDemoFit('cover')" id="demo-btn-cover">
                                        <i class="fas fa-crop"></i> เต็มกรอบ
                                    </button>
                                    <button type="button" class="btn btn-outline-primary" onclick="changeDemoFit('fill')" id="demo-btn-fill">
                                        <i class="fas fa-arrows-alt"></i> ยืดเต็ม
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="img-container-fixed img-size-xlarge">
                                <img src="https://picsum.photos/800/600" class="img-fit-contain" alt="Interactive Demo" id="demoImage">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="mb-0">คำอธิบาย</h5>
                        </div>
                        <div class="card-body">
                            <div id="modeDescription">
                                <h6 class="text-primary">โหมด: แสดงทั้งหมด (Contain)</h6>
                                <p>รูปภาพจะแสดงทั้งหมดโดยไม่ถูกตัด แต่อาจมีพื้นที่ว่างรอบๆ รูป เหมาะสำหรับรูปที่ต้องการให้เห็นเนื้อหาทั้งหมด</p>
                                <ul>
                                    <li><strong>ข้อดี:</strong> เห็นรูปภาพทั้งหมด ไม่มีการตัดทอน</li>
                                    <li><strong>ข้อเสีย:</strong> อาจมีพื้นที่ว่างรอบรูป</li>
                                    <li><strong>เหมาะสำหรับ:</strong> รูปที่มีเนื้อหาสำคัญทั่วทั้งภาพ</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Size Options Demo -->
        <div class="demo-section">
            <h2 class="demo-title">5. ขนาดกรอบรูปภาพ</h2>
            <div class="row g-4">
                <div class="col-md-2">
                    <div class="card">
                        <div class="card-image-container img-size-thumbnail">
                            <img src="https://picsum.photos/400/400" class="img-fit-cover" alt="Thumbnail">
                        </div>
                        <div class="card-body p-2">
                            <small><strong>Thumbnail</strong><br>100px</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card">
                        <div class="card-image-container img-size-small">
                            <img src="https://picsum.photos/400/400" class="img-fit-cover" alt="Small">
                        </div>
                        <div class="card-body p-2">
                            <small><strong>Small</strong><br>150px</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="card">
                        <div class="card-image-container img-size-medium">
                            <img src="https://picsum.photos/400/400" class="img-fit-cover" alt="Medium">
                        </div>
                        <div class="card-body p-2">
                            <small><strong>Medium</strong><br>200px</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-image-container img-size-large">
                            <img src="https://picsum.photos/400/400" class="img-fit-cover" alt="Large">
                        </div>
                        <div class="card-body p-2">
                            <small><strong>Large</strong><br>300px</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-image-container img-size-xlarge">
                            <img src="https://picsum.photos/400/400" class="img-fit-cover" alt="XLarge">
                        </div>
                        <div class="card-body p-2">
                            <small><strong>XLarge</strong><br>400px</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function changeDemoFit(fitMode) {
            const demoImage = document.getElementById('demoImage');
            const buttons = document.querySelectorAll('[id^="demo-btn-"]');
            const description = document.getElementById('modeDescription');
            
            // Remove all fit classes
            demoImage.classList.remove('img-fit-contain', 'img-fit-cover', 'img-fit-fill');
            
            // Add new fit class
            demoImage.classList.add('img-fit-' + fitMode);
            
            // Update button states
            buttons.forEach(btn => btn.classList.remove('active'));
            document.getElementById('demo-btn-' + fitMode).classList.add('active');
            
            // Update description
            const descriptions = {
                contain: {
                    title: 'แสดงทั้งหมด (Contain)',
                    text: 'รูปภาพจะแสดงทั้งหมดโดยไม่ถูกตัด แต่อาจมีพื้นที่ว่างรอบๆ รูป เหมาะสำหรับรูปที่ต้องการให้เห็นเนื้อหาทั้งหมด',
                    pros: 'เห็นรูปภาพทั้งหมด ไม่มีการตัดทอน',
                    cons: 'อาจมีพื้นที่ว่างรอบรูป',
                    use: 'รูปที่มีเนื้อหาสำคัญทั่วทั้งภาพ'
                },
                cover: {
                    title: 'เต็มกรอบ (Cover)',
                    text: 'รูปภาพจะเต็มกรอบทั้งหมด แต่อาจถูกตัดส่วนที่เกินออกไป เหมาะสำหรับการแสดงผลที่ต้องการความสวยงาม',
                    pros: 'เต็มกรอบ ดูสวยงาม ไม่มีพื้นที่ว่าง',
                    cons: 'อาจตัดเนื้อหาสำคัญออกไป',
                    use: 'รูปพื้นหลัง รูปตกแต่ง หรือรูปที่เนื้อหาสำคัญอยู่ตรงกลาง'
                },
                fill: {
                    title: 'ยืดเต็ม (Fill)',
                    text: 'รูปภาพจะถูกยืดให้เต็มกรอบทั้งความกว้างและความสูง อาจทำให้รูปดูบิดเบี้ยว ไม่แนะนำสำหรับการใช้งานทั่วไป',
                    pros: 'เต็มกรอบทั้งหมด ไม่มีพื้นที่ว่าง ไม่มีการตัด',
                    cons: 'รูปภาพอาจบิดเบี้ยว สัดส่วนไม่ถูกต้อง',
                    use: 'กรณีพิเศษที่ต้องการให้รูปเต็มกรอบแน่นอน'
                }
            };
            
            const desc = descriptions[fitMode];
            description.innerHTML = `
                <h6 class="text-primary">โหมด: ${desc.title}</h6>
                <p>${desc.text}</p>
                <ul>
                    <li><strong>ข้อดี:</strong> ${desc.pros}</li>
                    <li><strong>ข้อเสีย:</strong> ${desc.cons}</li>
                    <li><strong>เหมาะสำหรับ:</strong> ${desc.use}</li>
                </ul>
            `;
        }
    </script>
</body>
</html>
