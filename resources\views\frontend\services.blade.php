@extends('layouts.app')

@section('title', 'บริการ - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ'))

@section('content')
<!-- Page Header -->
<section class="hero-section">
    <div class="container">
        <div class="text-center">
            <h1 class="display-4 fw-bold mb-4">บริการของเรา</h1>
            <p class="lead">บริการจัดงานศพที่ครบครันและเหมาะสมกับทุกความต้องการ</p>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="py-5">
    <div class="container">
        @if($services->count() > 0)
        <div class="row g-4">
            @foreach($services as $service)
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100">
                    @if($service->image)
                    <img src="{{ asset('storage/' . $service->image) }}" class="card-img-top" alt="{{ $service->title }}" style="height: 250px; object-fit: cover;">
                    @else
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                        <i class="fas fa-praying-hands fa-4x text-muted"></i>
                    </div>
                    @endif

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ $service->title }}</h5>
                        <p class="card-text flex-grow-1">{{ $service->description }}</p>

                        @if($service->details)
                        <div class="mb-3">
                            <h6>รายละเอียด:</h6>
                            <p class="small text-muted">{{ Str::limit($service->details, 150) }}</p>
                        </div>
                        @endif

                        <div class="mt-auto">
                            <div class="text-center">
                                <small class="text-muted d-block mb-2">สอบถามราคาและรายละเอียดได้ที่เจ้าหน้าที่</small>
                                <a href="{{ route('contact') }}" class="btn btn-primary w-100">ติดต่อสอบถาม</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        @else
        <div class="text-center py-5">
            <i class="fas fa-praying-hands fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีบริการ</h3>
            <p class="text-muted">กรุณาติดต่อเราเพื่อสอบถามบริการจัดงานศพ</p>
            <a href="{{ route('contact') }}" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        @endif
    </div>
</section>

<!-- Contact CTA Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอด 24 ชั่วโมง</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                    </a>
                    @if(!empty($settings['line_id']))
                    <a href="https://line.me/ti/p/{{ $settings['line_id'] }}" class="btn btn-success btn-lg" target="_blank">
                        <i class="fab fa-line me-2"></i>Line
                    </a>
                    @endif
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการตลอด 24 ชั่วโมง ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
